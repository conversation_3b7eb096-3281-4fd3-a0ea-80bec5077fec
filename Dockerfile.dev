# ---- Development Environment Dockerfile ----
# 专用于开发环境的 Dockerfile，支持热重载和快速开发

FROM golang:1.24-alpine

LABEL stage="admin-api-development"
LABEL description="Development environment for admin-api with hot reload support"
LABEL maintainer="admin-api-team"

# 设置环境变量
ENV CGO_ENABLED=1
ENV GO_ENV=development
ENV TZ=Asia/Shanghai

# 安装开发所需的依赖
RUN apk update && apk add --no-cache \
    gcc \
    make \
    git \
    libc-dev \
    librdkafka-dev \
    bash \
    curl \
    wget \
    tzdata \
    vim \
    nano \
    htop \
    procps \
 && rm -rf /var/cache/apk/*

# 设置时区
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && \
    echo $TZ > /etc/timezone

# 安装 gf 工具
RUN wget -O gf https://github.com/gogf/gf/releases/latest/download/gf_$(go env GOOS)_$(go env GOARCH) && \
    chmod +x gf && \
    ./gf install -y && \
    rm ./gf

# 验证 gf 安装
RUN gf version

# 设置工作目录
WORKDIR /app

# 创建开发用户和组
RUN addgroup -S devgroup && adduser -S -G devgroup devuser

# 创建必要的目录结构
RUN mkdir -p /app/logs && \
    mkdir -p /app/manifest/config && \
    mkdir -p /app/manifest/i18n && \
    mkdir -p /app/tmp && \
    mkdir -p /go/pkg/mod && \
    mkdir -p /root/.cache/go-build

# 设置目录权限
RUN chown -R devuser:devgroup /app && \
    chown -R devuser:devgroup /go && \
    chown -R devuser:devgroup /root/.cache

# 切换到开发用户
USER devuser

# 设置 Go 环境变量
ENV GOPROXY=https://goproxy.cn,direct
ENV GOSUMDB=sum.golang.google.cn
ENV GO111MODULE=on

# 暴露端口
EXPOSE 7999
# 可选的调试端口
EXPOSE 2345

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl --fail http://localhost:7999/health || exit 1

# 设置启动命令
# 使用 gf run 支持热重载
CMD ["gf", "run", "main.go"]
