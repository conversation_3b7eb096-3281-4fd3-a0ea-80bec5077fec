#!/bin/bash

# 开发环境启动脚本
# 使用方法: ./dev-start.sh [command]
# 命令选项:
#   up      - 启动开发环境 (默认)
#   down    - 停止开发环境
#   restart - 重启开发环境
#   logs    - 查看日志
#   shell   - 进入容器 shell
#   build   - 重新构建镜像

set -e

COMPOSE_FILE="docker-compose.dev.yml"
SERVICE_NAME="admin-api-dev"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查 Docker 和 docker-compose 是否安装
check_dependencies() {
    if ! command -v docker &> /dev/null; then
        print_message $RED "错误: Docker 未安装或不在 PATH 中"
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null; then
        print_message $RED "错误: docker-compose 未安装或不在 PATH 中"
        exit 1
    fi
}

# 检查网络是否存在
check_network() {
    if ! docker network ls | grep -q "xpay_app-network"; then
        print_message $YELLOW "警告: xpay_app-network 网络不存在，正在创建..."
        docker network create xpay_app-network
        print_message $GREEN "网络 xpay_app-network 创建成功"
    fi
}

# 启动开发环境
start_dev() {
    print_message $BLUE "启动开发环境..."
    check_network
    docker-compose -f $COMPOSE_FILE up -d
    print_message $GREEN "开发环境启动成功!"
    print_message $YELLOW "应用将在 http://localhost:7999 上运行"
    print_message $YELLOW "使用 './dev-start.sh logs' 查看日志"
}

# 停止开发环境
stop_dev() {
    print_message $BLUE "停止开发环境..."
    docker-compose -f $COMPOSE_FILE down
    print_message $GREEN "开发环境已停止"
}

# 重启开发环境
restart_dev() {
    print_message $BLUE "重启开发环境..."
    docker-compose -f $COMPOSE_FILE restart
    print_message $GREEN "开发环境重启成功!"
}

# 查看日志
show_logs() {
    print_message $BLUE "显示开发环境日志..."
    docker-compose -f $COMPOSE_FILE logs -f $SERVICE_NAME
}

# 进入容器 shell
enter_shell() {
    print_message $BLUE "进入容器 shell..."
    docker-compose -f $COMPOSE_FILE exec $SERVICE_NAME /bin/bash
}

# 重新构建镜像
rebuild() {
    print_message $BLUE "重新构建开发镜像..."
    docker-compose -f $COMPOSE_FILE build --no-cache
    print_message $GREEN "镜像构建完成!"
}

# 显示帮助信息
show_help() {
    echo "开发环境管理脚本"
    echo ""
    echo "使用方法: $0 [command]"
    echo ""
    echo "命令选项:"
    echo "  up      - 启动开发环境 (默认)"
    echo "  down    - 停止开发环境"
    echo "  restart - 重启开发环境"
    echo "  logs    - 查看日志"
    echo "  shell   - 进入容器 shell"
    echo "  build   - 重新构建镜像"
    echo "  help    - 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0          # 启动开发环境"
    echo "  $0 up       # 启动开发环境"
    echo "  $0 logs     # 查看日志"
    echo "  $0 shell    # 进入容器"
}

# 主函数
main() {
    check_dependencies
    
    local command=${1:-up}
    
    case $command in
        up|start)
            start_dev
            ;;
        down|stop)
            stop_dev
            ;;
        restart)
            restart_dev
            ;;
        logs)
            show_logs
            ;;
        shell|bash)
            enter_shell
            ;;
        build|rebuild)
            rebuild
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_message $RED "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
