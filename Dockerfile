# ---- Production Builder Stage ----
# 建议使用更具体的版本标签以确保构建的可重现性，例如 golang:1.24.0-alpine3.19
FROM golang:1.24-alpine AS builder

LABEL stage="admin-api-builder"

ENV CGO_ENABLED=1

RUN apk update && apk add --no-cache \
    gcc \
    make \
    git \
    libc-dev \
    librdkafka-dev \
 && rm -rf /var/cache/apk/*

# Set timezone
ENV TZ=Asia/Shanghai
RUN apk --no-cache add tzdata && \
    ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && \
    echo $TZ > /etc/timezone

WORKDIR /build

COPY go.mod go.sum ./
RUN go mod download

# 强烈建议添加 .dockerignore 文件
COPY . .

RUN go build -tags musl -ldflags '-w -s -extldflags "-static"' -o ./admin_api cmd/main.go

# ---- Production Runtime Stage ----
# 使用更具体的 alpine 版本
FROM alpine:3.19 AS runtime

LABEL stage="admin-api-runtime"

WORKDIR /app

# 创建非 root 用户和组
RUN addgroup -S appgroup && adduser -S -G appgroup appuser

# 复制应用程序二进制文件和入口点脚本
# 这些文件将首先归 root 所有
COPY --from=builder /build/admin_api /app/admin_api
COPY --from=builder /build/entrypoint.sh /app/entrypoint.sh

# 为配置、i18n 文件和日志创建目录结构
# 这些目录也将首先归 root 所有
RUN mkdir -p /app/manifest/config && \
    mkdir -p /app/manifest/i18n && \
    mkdir -p /app/logs

# 复制配置文件模板和 i18n 文件
# 这些文件也将首先归 root 所有
COPY --from=builder /build/manifest/config/config.yaml.template /app/manifest/config/config.yaml.template
COPY --from=builder /build/manifest/i18n/ /app/manifest/i18n/

# 安装运行时依赖项并设置可执行权限
RUN apk update && apk add --no-cache \
    bash \
    jq \
    gettext \
    curl \
    tzdata \
 && chmod +x /app/entrypoint.sh \
 && chmod +x /app/admin_api \
 && rm -rf /var/cache/apk/*

# 更改 /app 目录及其所有内容的所有权给 appuser
# 这将确保 appuser 对 /app/manifest/config 和 /app/logs 具有写权限
RUN chown -R appuser:appgroup /app

# 切换到非 root 用户
USER appuser

# 设置入口点脚本 (建议使用绝对路径)
ENTRYPOINT [ "/app/entrypoint.sh" ]

# 默认运行 admin_api 应用程序的命令 (建议使用绝对路径)
CMD ["/app/admin_api"]

# 可选：添加 HEALTHCHECK 指令来检查应用程序的健康状况
# 注意：根据您的 config_variables.json，健康检查端口是 8081
# HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
#   CMD curl --fail http://localhost:8081/health || exit 1

# ---- Development Stage ----
FROM golang:1.24-alpine AS development

LABEL stage="admin-api-development"

ENV CGO_ENABLED=1

# 安装开发所需的依赖
RUN apk update && apk add --no-cache \
    gcc \
    make \
    git \
    libc-dev \
    librdkafka-dev \
    bash \
    curl \
    wget \
    tzdata \
 && rm -rf /var/cache/apk/*

# Set timezone
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && \
    echo $TZ > /etc/timezone

# 安装 gf 工具
RUN wget -O gf https://github.com/gogf/gf/releases/latest/download/gf_$(go env GOOS)_$(go env GOARCH) && \
    chmod +x gf && \
    ./gf install -y && \
    rm ./gf

WORKDIR /app

# 创建开发用户
RUN addgroup -S devgroup && adduser -S -G devgroup devuser

# 创建必要的目录
RUN mkdir -p /app/logs && \
    mkdir -p /app/manifest/config && \
    mkdir -p /app/manifest/i18n

# 更改目录所有权
RUN chown -R devuser:devgroup /app

# 切换到开发用户
USER devuser

# 暴露端口
EXPOSE 7999

# 默认命令：使用 gf run 启动应用
CMD ["gf", "run", "main.go"]
