# Docker 环境使用指南

本项目提供了分离的 Docker 开发环境和生产环境，支持代码热重载和快速开发。

## 文件说明

### 开发环境
- `Dockerfile.dev` - 开发环境专用 Dockerfile
- `docker-compose.dev.yml` - 开发环境的 Docker Compose 配置
- `dev-start.sh` - 开发环境管理脚本

### 生产环境
- `Dockerfile` - 生产环境专用 Dockerfile（多阶段构建）
- `docker-compose.yml` - 生产环境的 Docker Compose 配置
- `prod-start.sh` - 生产环境管理脚本

### 通用文件
- `.dockerignore` - Docker 构建时忽略的文件

## 快速开始

### 开发环境

#### 1. 启动开发环境

```bash
# 使用管理脚本启动（推荐）
./dev-start.sh

# 或者直接使用 docker-compose
docker-compose -f docker-compose.dev.yml up -d
```

#### 2. 查看日志

```bash
# 使用管理脚本
./dev-start.sh logs

# 或者直接使用 docker-compose
docker-compose -f docker-compose.dev.yml logs -f admin-api-dev
```

#### 3. 进入容器

```bash
# 使用管理脚本
./dev-start.sh shell

# 或者直接使用 docker
docker-compose -f docker-compose.dev.yml exec admin-api-dev /bin/bash
```

#### 4. 停止开发环境

```bash
# 使用管理脚本
./dev-start.sh down

# 或者直接使用 docker-compose
docker-compose -f docker-compose.dev.yml down
```

### 生产环境

#### 1. 启动生产环境

```bash
# 使用管理脚本启动（推荐）
./prod-start.sh

# 或者直接使用 docker-compose
docker-compose up -d
```

#### 2. 查看状态和日志

```bash
# 查看状态
./prod-start.sh status

# 查看日志
./prod-start.sh logs

# 或者直接使用 docker-compose
docker-compose ps
docker-compose logs -f admin-api
```

#### 3. 停止生产环境

```bash
# 使用管理脚本
./prod-start.sh down

# 或者直接使用 docker-compose
docker-compose down
```

## 环境特性对比

### 开发环境特性 🚀

#### 🔥 热重载
- 使用 `gf run main.go` 启动应用，支持代码变更时自动重启
- 本地代码目录直接挂载到容器内，修改代码立即生效

#### 📦 依赖缓存
- Go 模块缓存持久化，避免重复下载依赖
- Go 构建缓存持久化，加速编译过程

#### 🛠️ 开发工具
- 预装 gf 工具链
- 包含所有必要的编译依赖
- 包含调试工具：vim, nano, htop, procps

#### 🔧 环境配置
- 自动挂载 `config_variables.json` 配置文件
- 日志目录挂载，便于查看和调试
- 支持调试端口（2345）

### 生产环境特性 🏭

#### ⚡ 性能优化
- 多阶段构建，最小化镜像大小
- 静态编译的二进制文件，无外部依赖
- 基于 Alpine Linux，安全且轻量

#### 🔒 安全性
- 非 root 用户运行
- 最小化的运行时依赖
- 健康检查支持

#### 📊 监控支持
- 内置健康检查
- 结构化日志输出
- 资源使用监控

## 管理脚本使用

### 开发环境脚本

`dev-start.sh` 脚本提供了便捷的开发环境管理功能：

```bash
./dev-start.sh [command]
```

#### 开发环境可用命令

| 命令 | 说明 |
|------|------|
| `up` 或 `start` | 启动开发环境（默认） |
| `down` 或 `stop` | 停止开发环境 |
| `restart` | 重启开发环境 |
| `logs` | 查看实时日志 |
| `shell` 或 `bash` | 进入容器 shell |
| `build` 或 `rebuild` | 重新构建镜像 |
| `help` | 显示帮助信息 |

### 生产环境脚本

`prod-start.sh` 脚本提供了生产环境管理功能：

```bash
./prod-start.sh [command]
```

#### 生产环境可用命令

| 命令 | 说明 |
|------|------|
| `up` 或 `start` | 启动生产环境（默认） |
| `down` 或 `stop` | 停止生产环境 |
| `restart` | 重启生产环境 |
| `logs` | 查看实时日志 |
| `status` | 查看状态和资源使用 |
| `build` 或 `rebuild` | 重新构建镜像 |
| `help` | 显示帮助信息 |

### 使用示例

```bash
# 开发环境
./dev-start.sh          # 启动开发环境
./dev-start.sh logs     # 查看日志
./dev-start.sh shell    # 进入容器调试
./dev-start.sh down     # 停止环境

# 生产环境
./prod-start.sh         # 启动生产环境
./prod-start.sh status  # 查看状态
./prod-start.sh logs    # 查看日志
./prod-start.sh down    # 停止环境
```

## 网络配置

开发环境使用 `xpay_app-network` 外部网络，如果网络不存在，启动脚本会自动创建。

## 端口映射

- `7999:7999` - 主应用端口
- 如需调试端口，可在 `docker-compose.dev.yml` 中取消注释 `2345:2345`

## 目录挂载

| 本地路径 | 容器路径 | 说明 |
|----------|----------|------|
| `.` | `/app` | 项目根目录，支持热重载 |
| `./config_variables.json` | `/home/<USER>
| `./logs` | `/app/logs` | 日志目录 |

## 故障排除

### 1. 网络问题
如果遇到网络连接问题，检查 `xpay_app-network` 是否存在：
```bash
docker network ls | grep xpay_app-network
```

### 2. 权限问题
如果遇到文件权限问题，检查容器内的用户权限：
```bash
./dev-start.sh shell
whoami
ls -la /app
```

### 3. 端口冲突
如果端口 7999 被占用，可以修改 `docker-compose.dev.yml` 中的端口映射。

### 4. 重新构建
如果遇到依赖或构建问题，尝试重新构建镜像：
```bash
./dev-start.sh build
```

## 环境选择指南

### 何时使用开发环境
- 本地开发和调试
- 需要代码热重载
- 需要进入容器进行调试
- 测试新功能

### 何时使用生产环境
- 生产部署
- 性能测试
- 预发布环境
- CI/CD 流水线

## 注意事项

### 开发环境
1. 镜像较大，包含完整的 Go 开发工具链
2. 首次构建可能需要较长时间下载依赖
3. 确保本地有足够的磁盘空间用于 Go 缓存
4. 不适用于生产部署

### 生产环境
1. 确保 `config_variables.json` 文件存在且配置正确
2. 生产环境使用优化的静态二进制文件
3. 建议在生产环境中使用具体的镜像标签而非 `latest`
4. 定期备份配置文件和日志

## 文件结构总览

```
.
├── Dockerfile              # 生产环境 Dockerfile
├── Dockerfile.dev          # 开发环境 Dockerfile
├── docker-compose.yml      # 生产环境 compose 配置
├── docker-compose.dev.yml  # 开发环境 compose 配置
├── dev-start.sh            # 开发环境管理脚本
├── prod-start.sh           # 生产环境管理脚本
├── .dockerignore           # Docker 构建忽略文件
└── DEV_ENVIRONMENT.md      # 本文档
```
