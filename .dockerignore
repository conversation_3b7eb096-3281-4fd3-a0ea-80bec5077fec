# Git
.git
.gitignore
.gitattributes

# Documentation
*.md
README*
CHANGELOG*
LICENSE*
docs/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Build artifacts
dist/
build/
target/
*.exe
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool
*.out

# Go workspace file
go.work

# Temporary files
tmp/
temp/
*.tmp

# Config files that shouldn't be in the image
config_variables.json

# Scripts
scripts/
test_*.sh
*.sh

# Development tools
.air.toml
air_tmp/

# Backup files
*.backup
*.bak

# Archive files
*.zip
*.tar.gz
*.rar

# Database files
*.db
*.sqlite
*.sqlite3

# Cache
.cache/
