services:
  admin-api:
    build:
      context: ./
      dockerfile: Dockerfile.dev  # 使用独立的开发 Dockerfile
    restart: unless-stopped
    volumes:
      # 挂载整个项目目录到容器内，实现代码热重载
      - .:/app
      # 挂载 Go 模块缓存，加速依赖下载
      - go-mod-cache:/go/pkg/mod
      # 挂载 Go 构建缓存，加速编译
      - go-build-cache:/root/.cache/go-build
      # 挂载配置文件
      - ./config_variables.json:/home/<USER>
      # 挂载日志目录
      - ./logs:/app/logs
    environment:
      # entrypoint.sh 需要这个变量来找到变量文件
      - PATH_TO_SECRET_FILE=/home/<USER>
      # 开发环境变量
      - GO_ENV=development
      - CGO_ENABLED=1
    ports:
      # 映射 config.yaml 中定义的 eserver 和 healthCheck 端口
      - "7999:7999"
    networks:
      - xpay_app-network

    working_dir: /app
    # 使用 gf run 启动应用，支持热重载
    command: ["gf", "run", "main.go"]
    # 开发环境下保持容器运行，即使应用崩溃
    tty: true
    stdin_open: true

volumes:
  # Go 模块缓存卷，在容器重建时保持缓存
  go-mod-cache:
  # Go 构建缓存卷，加速编译
  go-build-cache:

networks:
  xpay_app-network:
    external: true