services:
  admin-api:
    image: admin-api:latest
    build:
      context: ./
      dockerfile: Dockerfile  # 使用生产环境 Dockerfile
      target: runtime  # 明确指定使用 runtime 阶段
    restart: unless-stopped
    volumes:
      - ./config_variables.json:/home/<USER>/home
      - ./logs:/home/<USER>/home
    environment:
      # entrypoint.sh 需要这个变量来找到变量文件
      - PATH_TO_SECRET_FILE=/home/<USER>
    ports:
      # 映射 config.yaml 中定义的 eserver 和 healthCheck 端口
      - "7999:7999"
    networks:
      - xpay_app-network

networks:
  xpay_app-network:
    external: true
