#!/bin/bash

# 生产环境启动脚本
# 使用方法: ./prod-start.sh [command]
# 命令选项:
#   up      - 启动生产环境 (默认)
#   down    - 停止生产环境
#   restart - 重启生产环境
#   logs    - 查看日志
#   status  - 查看状态
#   build   - 重新构建镜像

set -e

COMPOSE_FILE="docker-compose.yml"
SERVICE_NAME="admin-api"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查 Docker 和 docker-compose 是否安装
check_dependencies() {
    if ! command -v docker &> /dev/null; then
        print_message $RED "错误: Docker 未安装或不在 PATH 中"
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null; then
        print_message $RED "错误: docker-compose 未安装或不在 PATH 中"
        exit 1
    fi
}

# 检查网络是否存在
check_network() {
    if ! docker network ls | grep -q "xpay_app-network"; then
        print_message $YELLOW "警告: xpay_app-network 网络不存在，正在创建..."
        docker network create xpay_app-network
        print_message $GREEN "网络 xpay_app-network 创建成功"
    fi
}

# 检查配置文件
check_config() {
    if [ ! -f "config_variables.json" ]; then
        print_message $RED "错误: config_variables.json 配置文件不存在"
        print_message $YELLOW "请确保配置文件存在后再启动生产环境"
        exit 1
    fi
}

# 启动生产环境
start_prod() {
    print_message $BLUE "启动生产环境..."
    check_network
    check_config
    
    # 构建并启动
    docker-compose -f $COMPOSE_FILE up -d --build
    
    print_message $GREEN "生产环境启动成功!"
    print_message $YELLOW "应用将在 http://localhost:7999 上运行"
    print_message $YELLOW "使用 './prod-start.sh logs' 查看日志"
    print_message $YELLOW "使用 './prod-start.sh status' 查看状态"
}

# 停止生产环境
stop_prod() {
    print_message $BLUE "停止生产环境..."
    docker-compose -f $COMPOSE_FILE down
    print_message $GREEN "生产环境已停止"
}

# 重启生产环境
restart_prod() {
    print_message $BLUE "重启生产环境..."
    docker-compose -f $COMPOSE_FILE restart
    print_message $GREEN "生产环境重启成功!"
}

# 查看日志
show_logs() {
    print_message $BLUE "显示生产环境日志..."
    docker-compose -f $COMPOSE_FILE logs -f $SERVICE_NAME
}

# 查看状态
show_status() {
    print_message $BLUE "生产环境状态:"
    echo ""
    docker-compose -f $COMPOSE_FILE ps
    echo ""
    print_message $BLUE "容器资源使用情况:"
    docker stats --no-stream $(docker-compose -f $COMPOSE_FILE ps -q) 2>/dev/null || echo "无运行中的容器"
}

# 重新构建镜像
rebuild() {
    print_message $BLUE "重新构建生产镜像..."
    docker-compose -f $COMPOSE_FILE build --no-cache
    print_message $GREEN "镜像构建完成!"
}

# 显示帮助信息
show_help() {
    echo "生产环境管理脚本"
    echo ""
    echo "使用方法: $0 [command]"
    echo ""
    echo "命令选项:"
    echo "  up      - 启动生产环境 (默认)"
    echo "  down    - 停止生产环境"
    echo "  restart - 重启生产环境"
    echo "  logs    - 查看日志"
    echo "  status  - 查看状态"
    echo "  build   - 重新构建镜像"
    echo "  help    - 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0          # 启动生产环境"
    echo "  $0 up       # 启动生产环境"
    echo "  $0 logs     # 查看日志"
    echo "  $0 status   # 查看状态"
    echo ""
    echo "注意事项:"
    echo "  - 确保 config_variables.json 文件存在"
    echo "  - 生产环境使用优化的二进制文件"
    echo "  - 建议在生产环境中使用具体的镜像标签"
}

# 主函数
main() {
    check_dependencies
    
    local command=${1:-up}
    
    case $command in
        up|start)
            start_prod
            ;;
        down|stop)
            stop_prod
            ;;
        restart)
            restart_prod
            ;;
        logs)
            show_logs
            ;;
        status)
            show_status
            ;;
        build|rebuild)
            rebuild
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_message $RED "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
