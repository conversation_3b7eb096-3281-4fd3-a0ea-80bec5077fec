# 开发环境使用指南

本项目提供了完整的 Docker 开发环境，支持代码热重载和快速开发。

## 文件说明

- `Dockerfile` - 包含生产环境和开发环境的多阶段构建
- `docker-compose.dev.yml` - 开发环境的 Docker Compose 配置
- `dev-start.sh` - 开发环境管理脚本
- `.dockerignore` - Docker 构建时忽略的文件

## 快速开始

### 1. 启动开发环境

```bash
# 使用管理脚本启动（推荐）
./dev-start.sh

# 或者直接使用 docker-compose
docker-compose -f docker-compose.dev.yml up -d
```

### 2. 查看日志

```bash
# 使用管理脚本
./dev-start.sh logs

# 或者直接使用 docker-compose
docker-compose -f docker-compose.dev.yml logs -f admin-api-dev
```

### 3. 进入容器

```bash
# 使用管理脚本
./dev-start.sh shell

# 或者直接使用 docker
docker-compose -f docker-compose.dev.yml exec admin-api-dev /bin/bash
```

### 4. 停止开发环境

```bash
# 使用管理脚本
./dev-start.sh down

# 或者直接使用 docker-compose
docker-compose -f docker-compose.dev.yml down
```

## 开发环境特性

### 🔥 热重载
- 使用 `gf run main.go` 启动应用，支持代码变更时自动重启
- 本地代码目录直接挂载到容器内，修改代码立即生效

### 📦 依赖缓存
- Go 模块缓存持久化，避免重复下载依赖
- Go 构建缓存持久化，加速编译过程

### 🛠️ 开发工具
- 预装 gf 工具链
- 包含所有必要的编译依赖

### 🔧 环境配置
- 自动挂载 `config_variables.json` 配置文件
- 日志目录挂载，便于查看和调试

## 管理脚本使用

`dev-start.sh` 脚本提供了便捷的开发环境管理功能：

```bash
./dev-start.sh [command]
```

### 可用命令

| 命令 | 说明 |
|------|------|
| `up` 或 `start` | 启动开发环境（默认） |
| `down` 或 `stop` | 停止开发环境 |
| `restart` | 重启开发环境 |
| `logs` | 查看实时日志 |
| `shell` 或 `bash` | 进入容器 shell |
| `build` 或 `rebuild` | 重新构建镜像 |
| `help` | 显示帮助信息 |

### 使用示例

```bash
# 启动开发环境
./dev-start.sh

# 查看日志
./dev-start.sh logs

# 进入容器进行调试
./dev-start.sh shell

# 重新构建镜像（当 Dockerfile 变更时）
./dev-start.sh build

# 停止环境
./dev-start.sh down
```

## 网络配置

开发环境使用 `xpay_app-network` 外部网络，如果网络不存在，启动脚本会自动创建。

## 端口映射

- `7999:7999` - 主应用端口
- 如需调试端口，可在 `docker-compose.dev.yml` 中取消注释 `2345:2345`

## 目录挂载

| 本地路径 | 容器路径 | 说明 |
|----------|----------|------|
| `.` | `/app` | 项目根目录，支持热重载 |
| `./config_variables.json` | `/home/<USER>
| `./logs` | `/app/logs` | 日志目录 |

## 故障排除

### 1. 网络问题
如果遇到网络连接问题，检查 `xpay_app-network` 是否存在：
```bash
docker network ls | grep xpay_app-network
```

### 2. 权限问题
如果遇到文件权限问题，检查容器内的用户权限：
```bash
./dev-start.sh shell
whoami
ls -la /app
```

### 3. 端口冲突
如果端口 7999 被占用，可以修改 `docker-compose.dev.yml` 中的端口映射。

### 4. 重新构建
如果遇到依赖或构建问题，尝试重新构建镜像：
```bash
./dev-start.sh build
```

## 生产环境

生产环境仍使用原有的配置：
```bash
docker-compose up -d
```

这将使用 Dockerfile 中的 `runtime` 阶段构建优化的生产镜像。

## 注意事项

1. 开发环境镜像较大，包含完整的 Go 开发工具链
2. 首次构建可能需要较长时间下载依赖
3. 确保本地有足够的磁盘空间用于 Go 缓存
4. 开发环境不适用于生产部署
